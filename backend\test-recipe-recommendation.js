/**
 * 菜谱推荐算法测试脚本
 * 测试高级推荐功能和算法
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_URL = `${BASE_URL}/api`;

// 测试配置
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 测试用户数据
const testUser = {
  username: 'recommendation_tester',
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let authToken = '';

/**
 * 测试工具函数
 */
function logTest(testName) {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log('=' .repeat(50));
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message, error) {
  console.log(`❌ ${message}`);
  if (error.response) {
    console.log(`   Status: ${error.response.status}`);
    console.log(`   Message: ${error.response.data?.message || error.response.statusText}`);
  } else {
    console.log(`   Error: ${error.message}`);
  }
}

/**
 * 用户认证
 */
async function authenticate() {
  try {
    // 尝试注册用户
    try {
      await axios.post(`${API_URL}/auth/register`, testUser, testConfig);
      logSuccess('User registered successfully');
    } catch (error) {
      if (error.response?.status === 400 && error.response.data?.message?.includes('already exists')) {
        logSuccess('User already exists, continuing...');
      } else {
        throw error;
      }
    }
    
    // 登录用户
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    }, testConfig);
    
    authToken = loginResponse.data.data.token;
    logSuccess('User logged in successfully');
    
    return true;
  } catch (error) {
    logError('Authentication failed', error);
    return false;
  }
}

/**
 * 测试高级搜索功能
 */
async function testAdvancedSearch() {
  logTest('Advanced Recipe Search');
  
  try {
    // 测试基础搜索
    const basicSearch = await axios.get(`${API_URL}/recipes/search/advanced?keyword=鸡蛋&limit=5`, testConfig);
    
    if (basicSearch.data.success) {
      logSuccess(`Basic search found ${basicSearch.data.data.length} recipes`);
      basicSearch.data.data.forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name} (Score: ${recipe.search_score || 'N/A'})`);
      });
    }

    // 测试高级筛选
    const advancedSearch = await axios.get(
      `${API_URL}/recipes/search/advanced?keyword=&difficulty=easy&maxCookingTime=20&tags=快手菜&sortBy=cooking_time&limit=5`, 
      testConfig
    );
    
    if (advancedSearch.data.success) {
      logSuccess(`Advanced search found ${advancedSearch.data.data.length} recipes`);
      advancedSearch.data.data.forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name} (${recipe.difficulty}, ${recipe.cooking_time}min)`);
      });
    }

    return true;
  } catch (error) {
    logError('Advanced search failed', error);
    return false;
  }
}

/**
 * 测试基于食材的智能推荐
 */
async function testIngredientBasedRecommendation() {
  logTest('Ingredient-based Smart Recommendation');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    // 首先获取一些食材ID（模拟用户库存）
    const foodItemsResponse = await axios.get(`${API_URL}/inventory/food-items?limit=10`, testConfig);
    
    if (!foodItemsResponse.data.success || foodItemsResponse.data.data.length === 0) {
      logError('No food items available for testing', { message: 'Empty food items list' });
      return false;
    }

    // 模拟用户有的食材
    const mockIngredients = foodItemsResponse.data.data.slice(0, 3).map(item => ({
      food_item_id: item.id,
      quantity: Math.floor(Math.random() * 500) + 100 // 100-600g
    }));

    console.log('   Mock ingredients:', mockIngredients.map(ing => ing.food_item_id).join(', '));

    // 测试不同的匹配参数
    const testCases = [
      { minMatchRatio: 0.3, limit: 5, name: 'Low match ratio (30%)' },
      { minMatchRatio: 0.6, limit: 5, name: 'High match ratio (60%)' },
      { minMatchRatio: 0.1, limit: 3, sortBy: 'match_score', name: 'Best matches' }
    ];

    for (const testCase of testCases) {
      try {
        const response = await axios.post(
          `${API_URL}/recipes/recommend/by-ingredients`,
          {
            ingredients: mockIngredients,
            ...testCase
          },
          authConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.forEach((recipe, index) => {
            const matchRatio = recipe.match_stats?.match_ratio || 0;
            const canCook = recipe.match_stats?.can_cook ? '✓' : '✗';
            console.log(`     ${index + 1}. ${recipe.name} (Match: ${(matchRatio * 100).toFixed(1)}%, Can cook: ${canCook})`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Ingredient-based recommendation failed', error);
    return false;
  }
}

/**
 * 测试临期食材推荐
 */
async function testExpiringIngredientsRecommendation() {
  logTest('Expiring Ingredients Recommendation');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    // 测试不同的天数设置
    const testCases = [
      { daysAhead: 3, limit: 5, name: '3 days ahead' },
      { daysAhead: 7, limit: 5, name: '7 days ahead' },
      { daysAhead: 14, limit: 3, name: '14 days ahead' }
    ];

    for (const testCase of testCases) {
      try {
        const response = await axios.get(
          `${API_URL}/recipes/recommend/expiring?daysAhead=${testCase.daysAhead}&limit=${testCase.limit}`,
          authConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.forEach((recipe, index) => {
            const urgencyScore = recipe.urgency_score || 0;
            const expiringCount = recipe.expiring_ingredients?.length || 0;
            console.log(`     ${index + 1}. ${recipe.name} (Urgency: ${urgencyScore.toFixed(1)}, Expiring: ${expiringCount})`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Expiring ingredients recommendation failed', error);
    return false;
  }
}

/**
 * 测试个性化推荐
 */
async function testPersonalizedRecommendation() {
  logTest('Personalized Recipe Recommendation');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    // 先收藏几个菜谱来建立用户偏好
    const recipesResponse = await axios.get(`${API_URL}/recipes?limit=5`, testConfig);
    
    if (recipesResponse.data.success && recipesResponse.data.data.recipes.length > 0) {
      const recipes = recipesResponse.data.data.recipes;
      
      // 收藏前3个菜谱
      for (let i = 0; i < Math.min(3, recipes.length); i++) {
        try {
          await axios.post(`${API_URL}/recipes/${recipes[i].id}/favorite`, {}, authConfig);
          console.log(`   Favorited: ${recipes[i].name}`);
        } catch (error) {
          // 忽略收藏错误（可能已经收藏过）
        }
      }
    }

    // 测试个性化推荐
    const testCases = [
      { limit: 5, diversityFactor: 0.1, name: 'Low diversity' },
      { limit: 5, diversityFactor: 0.5, name: 'High diversity' },
      { limit: 3, includeNewRecipes: false, name: 'Exclude new recipes' }
    ];

    for (const testCase of testCases) {
      try {
        const queryParams = new URLSearchParams({
          limit: testCase.limit,
          diversityFactor: testCase.diversityFactor || 0.3,
          includeNewRecipes: testCase.includeNewRecipes !== false ? 'true' : 'false'
        });

        const response = await axios.get(
          `${API_URL}/recipes/recommend/personalized?${queryParams}`,
          authConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.forEach((recipe, index) => {
            const personalScore = recipe.personal_score || 0;
            const reason = recipe.recommendation_reason || 'No reason';
            console.log(`     ${index + 1}. ${recipe.name} (Score: ${personalScore.toFixed(2)})`);
            console.log(`        Reason: ${reason}`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Personalized recommendation failed', error);
    return false;
  }
}

/**
 * 测试推荐算法性能
 */
async function testRecommendationPerformance() {
  logTest('Recommendation Algorithm Performance');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const performanceTests = [
      {
        name: 'Large ingredient list',
        endpoint: '/recipes/recommend/by-ingredients',
        method: 'POST',
        data: {
          ingredients: Array.from({ length: 20 }, (_, i) => ({
            food_item_id: `test-ingredient-${i}`,
            quantity: 100
          })),
          limit: 10
        }
      },
      {
        name: 'Complex search query',
        endpoint: '/recipes/search/advanced',
        method: 'GET',
        params: {
          keyword: '鸡蛋',
          difficulty: 'easy',
          maxCookingTime: 30,
          tags: '家常菜,快手菜',
          sortBy: 'relevance',
          limit: 50
        }
      },
      {
        name: 'Personalized recommendation',
        endpoint: '/recipes/recommend/personalized',
        method: 'GET',
        params: {
          limit: 20,
          diversityFactor: 0.5
        }
      }
    ];

    for (const test of performanceTests) {
      try {
        const startTime = Date.now();
        
        let response;
        if (test.method === 'POST') {
          response = await axios.post(`${API_URL}${test.endpoint}`, test.data, authConfig);
        } else {
          const queryParams = test.params ? '?' + new URLSearchParams(test.params).toString() : '';
          response = await axios.get(`${API_URL}${test.endpoint}${queryParams}`, authConfig);
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        if (response.data.success) {
          const resultCount = Array.isArray(response.data.data) ? response.data.data.length : 
                             response.data.data.recipes ? response.data.data.recipes.length : 0;
          logSuccess(`${test.name}: ${resultCount} results in ${duration}ms`);
        }
      } catch (error) {
        logError(`${test.name} performance test failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Performance testing failed', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runRecommendationTests() {
  console.log('🚀 Starting Recipe Recommendation Algorithm Tests');
  console.log('=' .repeat(60));
  
  try {
    // 1. 认证
    const authSuccess = await authenticate();
    
    // 2. 高级搜索测试
    await testAdvancedSearch();
    
    // 3. 需要认证的推荐功能测试
    if (authSuccess) {
      await testIngredientBasedRecommendation();
      await testExpiringIngredientsRecommendation();
      await testPersonalizedRecommendation();
      await testRecommendationPerformance();
    }
    
    console.log('\n🎉 All Recipe Recommendation tests completed!');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// 检查服务器是否运行
async function checkServerHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      console.log('✅ Server is running and healthy');
      return true;
    }
  } catch (error) {
    console.error('❌ Server health check failed. Please ensure the server is running on port 3001');
    return false;
  }
}

// 启动测试
async function main() {
  console.log('🔍 Checking server health...');
  
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    process.exit(1);
  }
  
  await runRecommendationTests();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runRecommendationTests,
  checkServerHealth
};