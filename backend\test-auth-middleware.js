const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testAuthMiddleware() {
  console.log('🧪 Testing Authentication Middleware...\n');

  try {
    // 首先注册一个测试用户并获取token
    console.log('1. Setting up test user...');
    const testUser = {
      username: `middleware_test_${Date.now()}`,
      email: `middleware_test_${Date.now()}@example.com`,
      password: 'TestPassword123!'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
    const token = registerResponse.data.data.token;
    console.log('✅ Test user created and token obtained');

    // 测试需要认证的端点（使用/me端点作为示例）
    console.log('\n2. Testing protected endpoint with valid token...');
    const validTokenResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ Protected endpoint accessible with valid token:', validTokenResponse.data.data.username);

    // 测试无token访问保护端点
    console.log('\n3. Testing protected endpoint without token...');
    try {
      await axios.get(`${BASE_URL}/auth/me`);
      console.log('❌ Should have been rejected');
    } catch (error) {
      console.log('✅ Protected endpoint properly rejected without token:', error.response.data.message);
    }

    // 测试无效token
    console.log('\n4. Testing protected endpoint with invalid token...');
    try {
      await axios.get(`${BASE_URL}/auth/me`, {
        headers: {
          Authorization: 'Bearer invalid-token-here'
        }
      });
      console.log('❌ Should have been rejected');
    } catch (error) {
      console.log('✅ Protected endpoint properly rejected invalid token:', error.response.data.message);
    }

    // 测试过期token（创建一个立即过期的token）
    console.log('\n5. Testing token expiration handling...');
    const jwt = require('jsonwebtoken');
    const expiredToken = jwt.sign(
      { id: 'test', email: '<EMAIL>' },
      process.env.JWT_SECRET || 'default-secret-key',
      { expiresIn: '1ms' } // 立即过期
    );
    
    // 等待一下确保token过期
    await new Promise(resolve => setTimeout(resolve, 10));
    
    try {
      await axios.get(`${BASE_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${expiredToken}`
        }
      });
      console.log('❌ Should have been rejected');
    } catch (error) {
      console.log('✅ Expired token properly rejected:', error.response.data.message);
    }

    console.log('\n🎉 All authentication middleware tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run tests
testAuthMiddleware();