const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api`;

// 测试配置
const TEST_CONFIG = {
  timeout: 10000,
  retries: 3,
  delay: 1000
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 等待函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 创建测试用户并获取token
async function createTestUser() {
  try {
    const userData = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'TestPassword123!'
    };

    // 注册用户
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, userData);
    
    if (registerResponse.status === 201) {
      // 登录获取token
      const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
        email: userData.email,
        password: userData.password
      });
      
      return loginResponse.data.token;
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.includes('already exists')) {
      // 用户已存在，尝试登录
      try {
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: '<EMAIL>',
          password: 'testpassword123'
        });
        return loginResponse.data.token;
      } catch (loginError) {
        log('Failed to login with existing user', 'red');
        return null;
      }
    }
    log(`Failed to create test user: ${error.message}`, 'red');
    return null;
  }
}

// 测试基础健康检查
async function testBasicHealthCheck() {
  log('\n=== 测试基础健康检查 ===', 'blue');
  
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    
    if (response.status === 200 && response.data.status === 'OK') {
      log('✅ 基础健康检查通过', 'green');
      log(`   状态: ${response.data.status}`);
      log(`   时间戳: ${response.data.timestamp}`);
      log(`   版本: ${response.data.version}`);
      return true;
    } else {
      log('❌ 基础健康检查失败', 'red');
      return false;
    }
  } catch (error) {
    log(`❌ 基础健康检查错误: ${error.message}`, 'red');
    return false;
  }
}

// 测试监控API健康检查
async function testMonitoringHealthCheck() {
  log('\n=== 测试监控API健康检查 ===', 'blue');
  
  try {
    const response = await axios.get(`${API_BASE}/monitoring/health`);
    
    if (response.status === 200 || response.status === 503) {
      log('✅ 监控健康检查通过', 'green');
      log(`   整体状态: ${response.data.status}`);
      log(`   检查项目数量: ${Object.keys(response.data.checks).length}`);
      
      // 显示各项检查结果
      for (const [checkName, result] of Object.entries(response.data.checks)) {
        const status = result.status === 'healthy' ? '✅' : '❌';
        log(`   ${status} ${checkName}: ${result.message} (${result.duration || 'N/A'})`);
      }
      
      return true;
    } else {
      log('❌ 监控健康检查失败', 'red');
      return false;
    }
  } catch (error) {
    log(`❌ 监控健康检查错误: ${error.message}`, 'red');
    return false;
  }
}

// 测试Ping接口
async function testPingEndpoint() {
  log('\n=== 测试Ping接口 ===', 'blue');
  
  try {
    const response = await axios.get(`${API_BASE}/monitoring/ping`);
    
    if (response.status === 200 && response.data.status === 'OK') {
      log('✅ Ping接口正常', 'green');
      log(`   运行时间: ${Math.round(response.data.uptime)}秒`);
      return true;
    } else {
      log('❌ Ping接口失败', 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Ping接口错误: ${error.message}`, 'red');
    return false;
  }
}

// 测试需要认证的监控接口
async function testAuthenticatedMonitoringEndpoints(token) {
  log('\n=== 测试需要认证的监控接口 ===', 'blue');
  
  const headers = { Authorization: `Bearer ${token}` };
  let passedTests = 0;
  const totalTests = 4;
  
  // 测试系统状态接口
  try {
    const statusResponse = await axios.get(`${API_BASE}/monitoring/status`, { headers });
    if (statusResponse.status === 200) {
      log('✅ 系统状态接口正常', 'green');
      log(`   健康状态: ${statusResponse.data.health.status}`);
      log(`   总请求数: ${statusResponse.data.stats.total}`);
      passedTests++;
    }
  } catch (error) {
    log(`❌ 系统状态接口错误: ${error.message}`, 'red');
  }
  
  // 测试系统指标接口
  try {
    const metricsResponse = await axios.get(`${API_BASE}/monitoring/metrics`, { headers });
    if (metricsResponse.status === 200) {
      log('✅ 系统指标接口正常', 'green');
      log(`   内存使用: ${metricsResponse.data.system.memory.used}MB / ${metricsResponse.data.system.memory.total}MB`);
      log(`   运行时间: ${metricsResponse.data.uptime.formatted}`);
      passedTests++;
    }
  } catch (error) {
    log(`❌ 系统指标接口错误: ${error.message}`, 'red');
  }
  
  // 测试警报接口
  try {
    const alertsResponse = await axios.get(`${API_BASE}/monitoring/alerts`, { headers });
    if (alertsResponse.status === 200) {
      log('✅ 警报接口正常', 'green');
      log(`   警报数量: ${alertsResponse.data.total}`);
      passedTests++;
    }
  } catch (error) {
    log(`❌ 警报接口错误: ${error.message}`, 'red');
  }
  
  // 测试监控摘要接口
  try {
    const summaryResponse = await axios.get(`${API_BASE}/monitoring/summary`, { headers });
    if (summaryResponse.status === 200) {
      log('✅ 监控摘要接口正常', 'green');
      log(`   系统状态: ${summaryResponse.data.systemStatus}`);
      log(`   活跃警报: ${summaryResponse.data.alerts.active}`);
      passedTests++;
    }
  } catch (error) {
    log(`❌ 监控摘要接口错误: ${error.message}`, 'red');
  }
  
  return passedTests === totalTests;
}

// 测试日志记录功能
async function testLoggingFunctionality(token) {
  log('\n=== 测试日志记录功能 ===', 'blue');
  
  const headers = { Authorization: `Bearer ${token}` };
  let passedTests = 0;
  const totalTests = 3;
  
  // 生成一些测试请求来产生日志
  log('生成测试请求以产生日志...', 'yellow');
  
  try {
    // 正常请求
    await axios.get(`${API_BASE}/monitoring/ping`);
    
    // 错误请求
    try {
      await axios.get(`${API_BASE}/nonexistent-endpoint`);
    } catch (error) {
      // 预期的404错误
    }
    
    // 等待日志写入
    await wait(1000);
    
    // 测试请求日志
    const requestLogsResponse = await axios.get(`${API_BASE}/monitoring/logs/requests?lines=10`, { headers });
    if (requestLogsResponse.status === 200 && requestLogsResponse.data.logs.length > 0) {
      log('✅ 请求日志记录正常', 'green');
      log(`   日志条数: ${requestLogsResponse.data.logs.length}`);
      passedTests++;
    }
    
    // 测试错误日志
    const errorLogsResponse = await axios.get(`${API_BASE}/monitoring/logs/errors?lines=10`, { headers });
    if (errorLogsResponse.status === 200) {
      log('✅ 错误日志记录正常', 'green');
      log(`   错误日志条数: ${errorLogsResponse.data.logs.length}`);
      passedTests++;
    }
    
    // 测试系统日志
    const systemLogsResponse = await axios.get(`${API_BASE}/monitoring/logs/system?lines=10`, { headers });
    if (systemLogsResponse.status === 200) {
      log('✅ 系统日志记录正常', 'green');
      log(`   系统日志条数: ${systemLogsResponse.data.logs.length}`);
      passedTests++;
    }
    
  } catch (error) {
    log(`❌ 日志功能测试错误: ${error.message}`, 'red');
  }
  
  return passedTests === totalTests;
}

// 测试警报系统
async function testAlertSystem(token) {
  log('\n=== 测试警报系统 ===', 'blue');
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // 获取当前警报
    const alertsResponse = await axios.get(`${API_BASE}/monitoring/alerts`, { headers });
    
    if (alertsResponse.status === 200) {
      log('✅ 警报系统正常运行', 'green');
      
      const alerts = alertsResponse.data.alerts;
      if (alerts.length > 0) {
        log(`   发现 ${alerts.length} 个警报:`);
        alerts.slice(0, 3).forEach((alert, index) => {
          log(`   ${index + 1}. [${alert.severity.toUpperCase()}] ${alert.message}`);
        });
        
        // 测试警报确认功能
        const unacknowledgedAlert = alerts.find(alert => !alert.acknowledged);
        if (unacknowledgedAlert) {
          try {
            const ackResponse = await axios.post(
              `${API_BASE}/monitoring/alerts/${unacknowledgedAlert.id}/acknowledge`, 
              {}, 
              { headers }
            );
            if (ackResponse.status === 200) {
              log('✅ 警报确认功能正常', 'green');
            }
          } catch (ackError) {
            log(`❌ 警报确认功能错误: ${ackError.message}`, 'red');
          }
        }
      } else {
        log('   当前没有警报 (系统运行正常)', 'green');
      }
      
      return true;
    }
  } catch (error) {
    log(`❌ 警报系统测试错误: ${error.message}`, 'red');
    return false;
  }
}

// 测试统计功能
async function testStatisticsFeatures(token) {
  log('\n=== 测试统计功能 ===', 'blue');
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // 获取系统指标
    const metricsResponse = await axios.get(`${API_BASE}/monitoring/metrics`, { headers });
    
    if (metricsResponse.status === 200) {
      const data = metricsResponse.data;
      
      log('✅ 统计功能正常', 'green');
      log(`   总请求数: ${data.requests.total}`);
      log(`   错误数: ${data.requests.errors}`);
      log(`   错误率: ${data.requests.errorRate}`);
      log(`   内存使用率: ${data.system.memory.usage}%`);
      log(`   运行时间: ${data.uptime.formatted}`);
      
      return true;
    }
  } catch (error) {
    log(`❌ 统计功能测试错误: ${error.message}`, 'red');
    return false;
  }
}

// 检查日志文件是否创建
async function checkLogFiles() {
  log('\n=== 检查日志文件 ===', 'blue');
  
  const logDir = path.join(__dirname, 'logs');
  const expectedLogFiles = ['requests.log', 'errors.log', 'system.log'];
  
  let filesFound = 0;
  
  if (fs.existsSync(logDir)) {
    for (const logFile of expectedLogFiles) {
      const filePath = path.join(logDir, logFile);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        log(`✅ ${logFile} 存在 (大小: ${stats.size} bytes)`, 'green');
        filesFound++;
      } else {
        log(`❌ ${logFile} 不存在`, 'red');
      }
    }
  } else {
    log('❌ 日志目录不存在', 'red');
  }
  
  return filesFound === expectedLogFiles.length;
}

// 主测试函数
async function runMonitoringTests() {
  log('🚀 开始监控系统测试', 'blue');
  log('=' .repeat(50), 'blue');
  
  const results = {
    basicHealth: false,
    monitoringHealth: false,
    ping: false,
    authenticatedEndpoints: false,
    logging: false,
    alerts: false,
    statistics: false,
    logFiles: false
  };
  
  // 等待服务器启动
  log('等待服务器启动...', 'yellow');
  await wait(2000);
  
  // 基础测试（不需要认证）
  results.basicHealth = await testBasicHealthCheck();
  results.monitoringHealth = await testMonitoringHealthCheck();
  results.ping = await testPingEndpoint();
  
  // 创建测试用户获取token
  log('\n=== 创建测试用户 ===', 'blue');
  const token = await createTestUser();
  
  if (token) {
    log('✅ 测试用户创建成功', 'green');
    
    // 需要认证的测试
    results.authenticatedEndpoints = await testAuthenticatedMonitoringEndpoints(token);
    results.logging = await testLoggingFunctionality(token);
    results.alerts = await testAlertSystem(token);
    results.statistics = await testStatisticsFeatures(token);
  } else {
    log('❌ 无法创建测试用户，跳过需要认证的测试', 'red');
  }
  
  // 检查日志文件
  results.logFiles = await checkLogFiles();
  
  // 输出测试结果摘要
  log('\n' + '=' .repeat(50), 'blue');
  log('📊 测试结果摘要', 'blue');
  log('=' .repeat(50), 'blue');
  
  const testResults = [
    ['基础健康检查', results.basicHealth],
    ['监控健康检查', results.monitoringHealth],
    ['Ping接口', results.ping],
    ['认证监控接口', results.authenticatedEndpoints],
    ['日志记录功能', results.logging],
    ['警报系统', results.alerts],
    ['统计功能', results.statistics],
    ['日志文件创建', results.logFiles]
  ];
  
  let passedTests = 0;
  const totalTests = testResults.length;
  
  testResults.forEach(([testName, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    const color = passed ? 'green' : 'red';
    log(`${status} ${testName}`, color);
    if (passed) passedTests++;
  });
  
  log('\n' + '=' .repeat(50), 'blue');
  log(`总体结果: ${passedTests}/${totalTests} 测试通过`, passedTests === totalTests ? 'green' : 'yellow');
  
  if (passedTests === totalTests) {
    log('🎉 所有监控和日志功能测试通过！', 'green');
    return true;
  } else {
    log('⚠️  部分测试失败，请检查相关功能', 'yellow');
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMonitoringTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`❌ 测试运行错误: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = {
  runMonitoringTests,
  testBasicHealthCheck,
  testMonitoringHealthCheck,
  testPingEndpoint
};