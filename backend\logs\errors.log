{"timestamp":"2025-07-17T04:03:56.599Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/monitoring/health","ip":"::1","userAgent":"axios/1.10.0","statusCode":503,"responseTime":"3ms","contentLength":"781","userId":"anonymous","pid":19868,"memory":{"rss":117014528,"heapTotal":13692928,"heapUsed":12264744,"external":2186083,"arrayBuffers":18651},"uptime":52.0069251}
{"timestamp":"2025-07-17T04:03:56.628Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"21ms","contentLength":"154","userId":"anonymous","pid":19868,"memory":{"rss":117817344,"heapTotal":13955072,"heapUsed":13117704,"external":2186197,"arrayBuffers":18765},"uptime":52.0359486}
{"timestamp":"2025-07-17T04:04:17.989Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/monitoring/health","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","statusCode":503,"responseTime":"2ms","contentLength":"781","userId":"anonymous","pid":19868,"memory":{"rss":118345728,"heapTotal":15003648,"heapUsed":12772992,"external":2186197,"arrayBuffers":18651},"uptime":73.3971628}
{"timestamp":"2025-07-17T04:05:52.395Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/monitoring/health","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","statusCode":503,"responseTime":"2ms","contentLength":"783","userId":"anonymous","pid":19868,"memory":{"rss":118456320,"heapTotal":15265792,"heapUsed":12922512,"external":2186197,"arrayBuffers":18651},"uptime":167.8028374}
{"timestamp":"2025-07-17T06:03:52.837Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"12ms","contentLength":"154","userId":"anonymous","pid":19808,"memory":{"rss":118013952,"heapTotal":13955072,"heapUsed":13151544,"external":2186197,"arrayBuffers":18765},"uptime":25.9792682}
{"timestamp":"2025-07-17T06:04:13.417Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"1ms","contentLength":"154","userId":"anonymous","pid":19808,"memory":{"rss":118583296,"heapTotal":15003648,"heapUsed":12764048,"external":2186169,"arrayBuffers":18737},"uptime":46.5590491}
{"timestamp":"2025-07-17T06:04:13.709Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"axios/1.10.0","statusCode":401,"responseTime":"282ms","contentLength":"55","userId":"anonymous","pid":19808,"memory":{"rss":119787520,"heapTotal":15003648,"heapUsed":13268968,"external":2190251,"arrayBuffers":22819},"uptime":46.8505074}
{"timestamp":"2025-07-17T07:21:33.988Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/health","ip":"::ffff:127.0.0.1","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":2956,"memory":{"rss":74366976,"heapTotal":34140160,"heapUsed":14713400,"external":2549297,"arrayBuffers":20539},"uptime":0.7055006}
{"timestamp":"2025-07-17T07:21:33.999Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory","ip":"::ffff:127.0.0.1","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":2956,"memory":{"rss":74350592,"heapTotal":34402304,"heapUsed":15191576,"external":2551748,"arrayBuffers":23030},"uptime":0.7164388}
{"timestamp":"2025-07-17T07:21:34.003Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats","ip":"::ffff:127.0.0.1","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":2956,"memory":{"rss":74387456,"heapTotal":34402304,"heapUsed":15409424,"external":2552854,"arrayBuffers":24136},"uptime":0.7202392}
{"timestamp":"2025-07-17T07:21:34.008Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/nonexistent","ip":"::ffff:127.0.0.1","statusCode":404,"responseTime":"0ms","contentLength":"60","userId":"anonymous","pid":2956,"memory":{"rss":74461184,"heapTotal":34402304,"heapUsed":15646832,"external":2553960,"arrayBuffers":25242},"uptime":0.7249432}
{"timestamp":"2025-07-17T07:21:34.025Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::ffff:127.0.0.1","statusCode":200,"responseTime":"11ms","contentLength":0,"userId":"anonymous","error":{"message":"Unexpected token 'i', \"invalid json\" is not valid JSON","stack":"SyntaxError: Unexpected token 'i', \"invalid json\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)"},"pid":2956,"memory":{"rss":74747904,"heapTotal":34402304,"heapUsed":16648368,"external":2556054,"arrayBuffers":27336},"uptime":0.7424225}
{"timestamp":"2025-07-17T07:21:34.026Z","level":"ERROR","message":"Unexpected token 'i', \"invalid json\" is not valid JSON","stack":"SyntaxError: Unexpected token 'i', \"invalid json\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)","statusCode":400,"url":"/api/inventory","method":"POST","ip":"::ffff:127.0.0.1","pid":2956,"memory":{"rss":74756096,"heapTotal":34402304,"heapUsed":16669696,"external":2556054,"arrayBuffers":27336},"uptime":0.7430836}
{"timestamp":"2025-07-17T07:21:34.026Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::ffff:127.0.0.1","statusCode":400,"responseTime":"12ms","contentLength":"1162","userId":"anonymous","pid":2956,"memory":{"rss":74756096,"heapTotal":34402304,"heapUsed":16686296,"external":2556054,"arrayBuffers":27336},"uptime":0.7435969}
{"timestamp":"2025-07-17T07:22:07.927Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::ffff:127.0.0.1","statusCode":400,"responseTime":"16ms","contentLength":"154","userId":"anonymous","pid":22660,"memory":{"rss":74043392,"heapTotal":33615872,"heapUsed":14873024,"external":2548227,"arrayBuffers":19549},"uptime":0.6584139}
{"timestamp":"2025-07-17T07:24:27.305Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"11ms","contentLength":"154","userId":"anonymous","pid":32372,"memory":{"rss":57577472,"heapTotal":13955072,"heapUsed":12942712,"external":2212509,"arrayBuffers":18922},"uptime":59.5385791}
{"timestamp":"2025-07-17T07:25:35.704Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory?status=active&_t=1752737135698","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":32372,"memory":{"rss":59219968,"heapTotal":15527936,"heapUsed":13107328,"external":2212509,"arrayBuffers":18811},"uptime":127.9376919}
{"timestamp":"2025-07-17T07:25:35.708Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/stats?_t=1752737135699","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":32372,"memory":{"rss":59396096,"heapTotal":15527936,"heapUsed":13251400,"external":2212509,"arrayBuffers":18811},"uptime":127.9406994}
{"timestamp":"2025-07-17T07:25:35.709Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats?_t=1752737135700","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":32372,"memory":{"rss":59510784,"heapTotal":15527936,"heapUsed":13403064,"external":2212509,"arrayBuffers":18811},"uptime":127.9425854}
{"timestamp":"2025-07-17T08:46:01.889Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"16ms","contentLength":"154","userId":"anonymous","pid":15712,"memory":{"rss":59944960,"heapTotal":14479360,"heapUsed":13189744,"external":2338140,"arrayBuffers":18749},"uptime":38.0514063}
{"timestamp":"2025-07-17T08:52:17.899Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/login","ip":"::1","userAgent":"axios/1.10.0","statusCode":401,"responseTime":"255ms","contentLength":"55","userId":"anonymous","pid":15712,"memory":{"rss":69644288,"heapTotal":18567168,"heapUsed":16612920,"external":3575266,"arrayBuffers":99190},"uptime":414.0612709}
{"timestamp":"2025-07-17T08:52:17.904Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"2ms","contentLength":"74","userId":"anonymous","pid":15712,"memory":{"rss":69758976,"heapTotal":18567168,"heapUsed":16800968,"external":3575299,"arrayBuffers":99305},"uptime":414.0658043}
{"timestamp":"2025-07-17T08:52:17.908Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/auth/me","ip":"::1","userAgent":"axios/1.10.0","statusCode":401,"responseTime":"0ms","contentLength":"53","userId":"anonymous","pid":15712,"memory":{"rss":69758976,"heapTotal":18567168,"heapUsed":16880840,"external":3575299,"arrayBuffers":99305},"uptime":414.0695916}
{"timestamp":"2025-07-17T08:52:36.350Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":400,"responseTime":"0ms","contentLength":"57","userId":"anonymous","pid":15712,"memory":{"rss":69763072,"heapTotal":18567168,"heapUsed":17010128,"external":3575408,"arrayBuffers":99414},"uptime":432.512194}
{"timestamp":"2025-07-17T08:52:36.644Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/auth/me","ip":"::1","userAgent":"axios/1.10.0","statusCode":401,"responseTime":"0ms","contentLength":"53","userId":"anonymous","pid":15712,"memory":{"rss":70119424,"heapTotal":18829312,"heapUsed":17304944,"external":3579719,"arrayBuffers":99183},"uptime":432.8064074}
{"timestamp":"2025-07-17T08:52:36.651Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":429,"responseTime":"0ms","contentLength":"66","userId":"anonymous","pid":15712,"memory":{"rss":70131712,"heapTotal":18829312,"heapUsed":17501648,"external":3575304,"arrayBuffers":99310},"uptime":432.8125885}
{"timestamp":"2025-07-17T08:52:36.653Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":429,"responseTime":"0ms","contentLength":"66","userId":"anonymous","pid":15712,"memory":{"rss":70336512,"heapTotal":18829312,"heapUsed":16966480,"external":3571094,"arrayBuffers":95100},"uptime":432.8153816}
{"timestamp":"2025-07-17T08:52:50.822Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/auth/register","ip":"::1","userAgent":"axios/1.10.0","statusCode":429,"responseTime":"1ms","contentLength":"66","userId":"anonymous","pid":15712,"memory":{"rss":70340608,"heapTotal":18829312,"heapUsed":17073624,"external":3571227,"arrayBuffers":95233},"uptime":446.9837701}
{"timestamp":"2025-07-17T09:11:46.135Z","level":"FATAL","message":"Uncaught exception","error":"listen EADDRINUSE: address already in use :::3001","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\bingxiang\\backend\\src\\server.js:99:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","pid":27096,"memory":{"rss":69775360,"heapTotal":30994432,"heapUsed":12391592,"external":2364557,"arrayBuffers":19011},"uptime":0.5439956}
{"timestamp":"2025-07-17T09:12:07.637Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory?status=active&_t=1752743527610","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71364608,"heapTotal":21188608,"heapUsed":18376312,"external":3676673,"arrayBuffers":196116},"uptime":1603.7989976}
{"timestamp":"2025-07-17T09:12:07.640Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/stats?_t=1752743527610","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71372800,"heapTotal":21188608,"heapUsed":18474064,"external":3676673,"arrayBuffers":196116},"uptime":1603.8015978}
{"timestamp":"2025-07-17T09:12:07.642Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats?_t=1752743527611","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71438336,"heapTotal":21188608,"heapUsed":18558304,"external":3676673,"arrayBuffers":196116},"uptime":1603.8043167}
{"timestamp":"2025-07-17T09:12:15.691Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory?status=active&_t=1752743535682","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71454720,"heapTotal":21188608,"heapUsed":18816656,"external":3676673,"arrayBuffers":196116},"uptime":1611.8533348}
{"timestamp":"2025-07-17T09:12:15.694Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats?_t=1752743535682","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71458816,"heapTotal":21188608,"heapUsed":18902512,"external":3676673,"arrayBuffers":196116},"uptime":1611.8558553}
{"timestamp":"2025-07-17T09:12:15.697Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/stats?_t=1752743535682","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71458816,"heapTotal":21188608,"heapUsed":18983448,"external":3676673,"arrayBuffers":196116},"uptime":1611.8588677}
{"timestamp":"2025-07-17T09:12:18.882Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory?status=active&_t=1752743538825","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":18819984,"external":3672110,"arrayBuffers":196116},"uptime":1615.0442713}
{"timestamp":"2025-07-17T09:12:18.884Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/stats?_t=1752743538826","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":18901808,"external":3672110,"arrayBuffers":196116},"uptime":1615.0461568}
{"timestamp":"2025-07-17T09:12:18.887Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats?_t=1752743538826","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":18982928,"external":3672110,"arrayBuffers":196116},"uptime":1615.0491469}
{"timestamp":"2025-07-17T09:12:18.889Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory?status=active&_t=1752743538826","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":19065200,"external":3672110,"arrayBuffers":196116},"uptime":1615.0514698}
{"timestamp":"2025-07-17T09:12:18.891Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/expiry/stats?_t=1752743538827","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":19146176,"external":3672110,"arrayBuffers":196116},"uptime":1615.053269}
{"timestamp":"2025-07-17T09:12:18.893Z","level":"REQUEST","message":"API Request","method":"GET","url":"/api/inventory/stats?_t=1752743538826","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71680000,"heapTotal":20402176,"heapUsed":19228208,"external":3672110,"arrayBuffers":196116},"uptime":1615.0550369}
{"timestamp":"2025-07-17T09:12:36.775Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"1ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71901184,"heapTotal":20664320,"heapUsed":18846232,"external":3672221,"arrayBuffers":196227},"uptime":1632.9368573}
{"timestamp":"2025-07-17T09:12:39.578Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":401,"responseTime":"0ms","contentLength":"68","userId":"anonymous","pid":15712,"memory":{"rss":71909376,"heapTotal":20664320,"heapUsed":18940096,"external":3672332,"arrayBuffers":196338},"uptime":1635.7401693}
{"timestamp":"2025-07-17T09:14:16.700Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","contentLength":"141","userId":"e64327b2c033ede4ce59954bfbf58d83","pid":15712,"memory":{"rss":74620928,"heapTotal":21975040,"heapUsed":17245816,"external":3552007,"arrayBuffers":76013},"uptime":1732.8618838}
{"timestamp":"2025-07-17T09:14:17.721Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"2ms","contentLength":"141","userId":"e64327b2c033ede4ce59954bfbf58d83","pid":15712,"memory":{"rss":74620928,"heapTotal":21975040,"heapUsed":17383248,"external":3552118,"arrayBuffers":76124},"uptime":1733.8836957}
{"timestamp":"2025-07-17T09:14:19.740Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","contentLength":"141","userId":"e64327b2c033ede4ce59954bfbf58d83","pid":15712,"memory":{"rss":74625024,"heapTotal":21975040,"heapUsed":17538008,"external":3560421,"arrayBuffers":84427},"uptime":1735.9020444}
{"timestamp":"2025-07-17T09:14:23.753Z","level":"REQUEST","message":"API Request","method":"POST","url":"/api/inventory","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","contentLength":"141","userId":"e64327b2c033ede4ce59954bfbf58d83","pid":15712,"memory":{"rss":74633216,"heapTotal":21975040,"heapUsed":17727456,"external":3560532,"arrayBuffers":84538},"uptime":1739.9158212}
