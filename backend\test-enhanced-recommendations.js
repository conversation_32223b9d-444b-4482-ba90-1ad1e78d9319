/**
 * 增强菜谱推荐算法测试脚本
 * 测试新增的高级推荐功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_URL = `${BASE_URL}/api`;

// 测试配置
const testConfig = {
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 测试用户数据
const testUser = {
  username: 'enhanced_rec_tester',
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let authToken = '';

/**
 * 测试工具函数
 */
function logTest(testName) {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log('=' .repeat(60));
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message, error) {
  console.log(`❌ ${message}`);
  if (error.response) {
    console.log(`   Status: ${error.response.status}`);
    console.log(`   Message: ${error.response.data?.message || error.response.statusText}`);
  } else {
    console.log(`   Error: ${error.message}`);
  }
}

/**
 * 用户认证
 */
async function authenticate() {
  try {
    // 尝试注册用户
    try {
      await axios.post(`${API_URL}/auth/register`, testUser, testConfig);
      logSuccess('User registered successfully');
    } catch (error) {
      if (error.response?.status === 400 && error.response.data?.message?.includes('already exists')) {
        logSuccess('User already exists, continuing...');
      } else {
        throw error;
      }
    }
    
    // 登录用户
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    }, testConfig);
    
    authToken = loginResponse.data.data.token;
    logSuccess('User logged in successfully');
    
    return true;
  } catch (error) {
    logError('Authentication failed', error);
    return false;
  }
}

/**
 * 测试协同过滤推荐
 */
async function testCollaborativeRecommendations() {
  logTest('Collaborative Filtering Recommendations');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    // 先收藏一些菜谱来建立用户偏好
    const recipesResponse = await axios.get(`${API_URL}/recipes?limit=5`, testConfig);
    
    if (recipesResponse.data.success && recipesResponse.data.data.recipes.length > 0) {
      const recipes = recipesResponse.data.data.recipes;
      
      // 收藏前3个菜谱
      for (let i = 0; i < Math.min(3, recipes.length); i++) {
        try {
          await axios.post(`${API_URL}/recipes/${recipes[i].id}/favorite`, {}, authConfig);
          console.log(`   Favorited: ${recipes[i].name}`);
        } catch (error) {
          // 忽略收藏错误（可能已经收藏过）
        }
      }
    }

    // 测试协同过滤推荐
    const testCases = [
      { limit: 5, minSimilarity: 0.1, name: 'Low similarity threshold' },
      { limit: 3, minSimilarity: 0.5, name: 'High similarity threshold' },
      { limit: 10, minSimilarity: 0.3, name: 'Default settings' }
    ];

    for (const testCase of testCases) {
      try {
        const queryParams = new URLSearchParams({
          limit: testCase.limit,
          minSimilarity: testCase.minSimilarity
        });

        const response = await axios.get(
          `${API_URL}/recipes/recommend/collaborative?${queryParams}`,
          authConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.forEach((recipe, index) => {
            const collabScore = recipe.collaborative_score || 0;
            const similarUsers = recipe.similar_users_count || 0;
            console.log(`     ${index + 1}. ${recipe.name} (Score: ${collabScore}, Users: ${similarUsers})`);
            console.log(`        Reason: ${recipe.recommendation_reason || 'N/A'}`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Collaborative filtering test failed', error);
    return false;
  }
}

/**
 * 测试混合推荐算法
 */
async function testHybridRecommendations() {
  logTest('Hybrid Recommendation Algorithm');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const testCases = [
      { 
        limit: 5, 
        contentWeight: 0.5, 
        collaborativeWeight: 0.3, 
        inventoryWeight: 0.1, 
        popularityWeight: 0.1,
        name: 'Content-focused hybrid' 
      },
      { 
        limit: 5, 
        contentWeight: 0.2, 
        collaborativeWeight: 0.5, 
        inventoryWeight: 0.2, 
        popularityWeight: 0.1,
        name: 'Collaborative-focused hybrid' 
      },
      { 
        limit: 8, 
        contentWeight: 0.25, 
        collaborativeWeight: 0.25, 
        inventoryWeight: 0.25, 
        popularityWeight: 0.25,
        name: 'Balanced hybrid' 
      }
    ];

    for (const testCase of testCases) {
      try {
        const queryParams = new URLSearchParams({
          limit: testCase.limit,
          contentWeight: testCase.contentWeight,
          collaborativeWeight: testCase.collaborativeWeight,
          inventoryWeight: testCase.inventoryWeight,
          popularityWeight: testCase.popularityWeight
        });

        const response = await axios.get(
          `${API_URL}/recipes/recommend/hybrid?${queryParams}`,
          authConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.forEach((recipe, index) => {
            const hybridScore = recipe.hybrid_score || 0;
            const sources = recipe.recommendation_sources || [];
            console.log(`     ${index + 1}. ${recipe.name} (Score: ${hybridScore.toFixed(2)})`);
            console.log(`        Sources: ${sources.join(', ')}`);
            console.log(`        Reason: ${recipe.recommendation_reason || 'N/A'}`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Hybrid recommendation test failed', error);
    return false;
  }
}

/**
 * 测试智能菜单规划
 */
async function testWeeklyMenuPlanning() {
  logTest('Weekly Menu Planning');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const testCases = [
      { 
        days: 3, 
        mealsPerDay: 2, 
        diversityFactor: 0.5,
        name: '3-day weekend plan' 
      },
      { 
        days: 7, 
        mealsPerDay: 3, 
        diversityFactor: 0.8,
        name: 'Full week plan' 
      },
      { 
        days: 5, 
        mealsPerDay: 2, 
        diversityFactor: 0.9,
        name: 'Workweek plan (high diversity)' 
      }
    ];

    for (const testCase of testCases) {
      try {
        const response = await axios.post(
          `${API_URL}/recipes/menu/weekly`,
          {
            days: testCase.days,
            mealsPerDay: testCase.mealsPerDay,
            diversityFactor: testCase.diversityFactor,
            nutritionBalance: true
          },
          authConfig
        );

        if (response.data.success) {
          const menuPlan = response.data.data;
          logSuccess(`${testCase.name}: Generated ${menuPlan.menu_plan.length} days`);
          
          console.log(`     Total recipes: ${menuPlan.total_recipes}`);
          console.log(`     Planning date: ${new Date(menuPlan.planning_date).toLocaleDateString()}`);
          
          // 显示前几天的菜单
          menuPlan.menu_plan.slice(0, 2).forEach(day => {
            console.log(`     Day ${day.day}: ${day.meals.length} meals`);
            day.meals.forEach(meal => {
              console.log(`       ${meal.type}: ${meal.recipe.name} (${meal.estimated_prep_time}min)`);
            });
          });

          // 显示购物清单摘要
          if (menuPlan.recommendations.shop_for_ingredients) {
            const shoppingList = menuPlan.recommendations.shop_for_ingredients;
            console.log(`     Shopping list: ${shoppingList.length} items needed`);
            shoppingList.slice(0, 3).forEach(item => {
              console.log(`       - ${item.name}: ${item.quantity_needed}${item.unit}`);
            });
          }

          // 显示营养摘要
          if (menuPlan.nutrition_summary) {
            const nutrition = menuPlan.nutrition_summary;
            console.log(`     Nutrition: ${nutrition.calories} cal, ${nutrition.protein}g protein`);
          }
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Weekly menu planning test failed', error);
    return false;
  }
}

/**
 * 测试增强搜索功能
 */
async function testEnhancedSearch() {
  logTest('Enhanced Search Functionality');
  
  try {
    const testCases = [
      { 
        keyword: '西红柿 鸡蛋', 
        name: 'Multi-keyword search' 
      },
      { 
        keyword: '土豆', 
        difficulty: 'easy', 
        maxCookingTime: 15,
        name: 'Keyword with filters' 
      },
      { 
        keyword: '', 
        tags: '家常菜,快手菜', 
        sortBy: 'cooking_time',
        name: 'Tag-based search' 
      },
      { 
        keyword: '汤', 
        minCookingTime: 20, 
        maxCookingTime: 30,
        name: 'Time range search' 
      }
    ];

    for (const testCase of testCases) {
      try {
        const queryParams = new URLSearchParams();
        Object.keys(testCase).forEach(key => {
          if (key !== 'name' && testCase[key] !== undefined) {
            queryParams.append(key, testCase[key]);
          }
        });

        const response = await axios.get(
          `${API_URL}/recipes/search/advanced?${queryParams}`,
          testConfig
        );

        if (response.data.success) {
          logSuccess(`${testCase.name}: Found ${response.data.data.length} recipes`);
          response.data.data.slice(0, 3).forEach((recipe, index) => {
            const searchScore = recipe.search_score || 0;
            const popularityScore = recipe.popularity_score || 0;
            console.log(`     ${index + 1}. ${recipe.name} (Search: ${searchScore}, Pop: ${popularityScore})`);
            console.log(`        ${recipe.difficulty}, ${recipe.cooking_time}min, Rating: ${recipe.rating || 'N/A'}`);
          });
        }
      } catch (error) {
        logError(`${testCase.name} failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Enhanced search test failed', error);
    return false;
  }
}

/**
 * 测试推荐算法性能
 */
async function testRecommendationPerformance() {
  logTest('Recommendation Algorithm Performance');
  
  if (!authToken) {
    logError('Authentication required', { message: 'No auth token' });
    return false;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const performanceTests = [
      {
        name: 'Collaborative filtering',
        endpoint: '/recipes/recommend/collaborative',
        method: 'GET',
        params: { limit: 10, minSimilarity: 0.3 }
      },
      {
        name: 'Hybrid recommendations',
        endpoint: '/recipes/recommend/hybrid',
        method: 'GET',
        params: { limit: 15 }
      },
      {
        name: 'Weekly menu planning',
        endpoint: '/recipes/menu/weekly',
        method: 'POST',
        data: { days: 7, mealsPerDay: 3, diversityFactor: 0.8 }
      },
      {
        name: 'Enhanced search',
        endpoint: '/recipes/search/advanced',
        method: 'GET',
        params: { keyword: '鸡蛋', limit: 20 }
      }
    ];

    for (const test of performanceTests) {
      try {
        const startTime = Date.now();
        
        let response;
        if (test.method === 'POST') {
          response = await axios.post(`${API_URL}${test.endpoint}`, test.data, authConfig);
        } else {
          const queryParams = test.params ? '?' + new URLSearchParams(test.params).toString() : '';
          response = await axios.get(`${API_URL}${test.endpoint}${queryParams}`, authConfig);
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        if (response.data.success) {
          const resultCount = Array.isArray(response.data.data) ? response.data.data.length : 
                             response.data.data.menu_plan ? response.data.data.menu_plan.length :
                             response.data.data.recipes ? response.data.data.recipes.length : 1;
          logSuccess(`${test.name}: ${resultCount} results in ${duration}ms`);
          
          // 性能评估
          if (duration < 100) {
            console.log(`       Performance: Excellent (< 100ms)`);
          } else if (duration < 500) {
            console.log(`       Performance: Good (< 500ms)`);
          } else if (duration < 1000) {
            console.log(`       Performance: Acceptable (< 1s)`);
          } else {
            console.log(`       Performance: Needs optimization (> 1s)`);
          }
        }
      } catch (error) {
        logError(`${test.name} performance test failed`, error);
      }
    }

    return true;
  } catch (error) {
    logError('Performance testing failed', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runEnhancedRecommendationTests() {
  console.log('🚀 Starting Enhanced Recipe Recommendation Tests');
  console.log('=' .repeat(70));
  
  try {
    // 1. 认证
    const authSuccess = await authenticate();
    
    // 2. 增强搜索测试
    await testEnhancedSearch();
    
    // 3. 需要认证的高级推荐功能测试
    if (authSuccess) {
      await testCollaborativeRecommendations();
      await testHybridRecommendations();
      await testWeeklyMenuPlanning();
      await testRecommendationPerformance();
    }
    
    console.log('\n🎉 All Enhanced Recommendation tests completed!');
    console.log('=' .repeat(70));
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// 检查服务器是否运行
async function checkServerHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      console.log('✅ Server is running and healthy');
      return true;
    }
  } catch (error) {
    console.error('❌ Server health check failed. Please ensure the server is running on port 3001');
    return false;
  }
}

// 启动测试
async function main() {
  console.log('🔍 Checking server health...');
  
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    process.exit(1);
  }
  
  await runEnhancedRecommendationTests();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runEnhancedRecommendationTests,
  checkServerHealth
};