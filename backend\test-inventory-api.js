const request = require('supertest');
const app = require('./src/server');

/**
 * 测试库存管理API接口
 */
async function testInventoryAPI() {
  console.log('🧪 Testing Inventory Management API...\n');

  try {
    // 测试用户注册和登录
    console.log('📋 Test 1: User Authentication...');
    
    const testUser = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'TestPassword123!'
    };

    // 注册用户
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send(testUser);

    if (registerResponse.status !== 201) {
      throw new Error(`Registration failed: ${registerResponse.body.error}`);
    }

    console.log('   ✅ User registered successfully');

    // 登录获取token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });

    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${loginResponse.body.error}`);
    }

    const token = loginResponse.body.data.token;
    console.log('   ✅ User logged in successfully');
    console.log('✅ User Authentication tests passed\n');

    // 测试库存API
    console.log('📋 Test 2: Inventory API Endpoints...');

    // 获取空库存列表
    const emptyInventoryResponse = await request(app)
      .get('/api/inventory')
      .set('Authorization', `Bearer ${token}`);

    if (emptyInventoryResponse.status !== 200) {
      throw new Error(`Get inventory failed: ${emptyInventoryResponse.body.error}`);
    }

    console.log('   ✅ Get empty inventory list successful');
    console.log(`   📊 Initial inventory count: ${emptyInventoryResponse.body.count}`);

    // 创建测试食材
    const testFoodResponse = await request(app)
      .post('/api/auth/test-food')  // 假设有这个测试接口
      .set('Authorization', `Bearer ${token}`)
      .send({
        name: '测试苹果',
        category: '水果',
        default_expiry_days: 7
      });

    let foodItemId;
    if (testFoodResponse.status === 201) {
      foodItemId = testFoodResponse.body.data.id;
      console.log('   ✅ Test food item created');
    } else {
      // 如果没有测试接口，使用现有的食材
      const { FoodItem } = require('./src/models/FoodItem');
      const foodModel = new FoodItem();
      const existingFood = await foodModel.findByName('苹果') || await foodModel.findOne();
      if (existingFood) {
        foodItemId = existingFood.id;
        console.log('   ✅ Using existing food item');
      } else {
        throw new Error('No food items available for testing');
      }
    }

    // 添加库存项目
    const inventoryData = {
      food_item_id: foodItemId,
      quantity: 5,
      unit: '个',
      expiry_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
      location: '冷藏室',
      notes: '测试库存项目'
    };

    const addInventoryResponse = await request(app)
      .post('/api/inventory')
      .set('Authorization', `Bearer ${token}`)
      .send(inventoryData);

    if (addInventoryResponse.status !== 201) {
      throw new Error(`Add inventory failed: ${addInventoryResponse.body.error}`);
    }

    const inventoryItemId = addInventoryResponse.body.data.id;
    console.log('   ✅ Inventory item added successfully');
    console.log(`   📦 Item ID: ${inventoryItemId}`);

    // 获取库存列表
    const inventoryListResponse = await request(app)
      .get('/api/inventory')
      .set('Authorization', `Bearer ${token}`);

    if (inventoryListResponse.status !== 200) {
      throw new Error(`Get inventory list failed: ${inventoryListResponse.body.error}`);
    }

    console.log('   ✅ Get inventory list successful');
    console.log(`   📊 Inventory count: ${inventoryListResponse.body.count}`);

    // 获取库存统计
    const statsResponse = await request(app)
      .get('/api/inventory/stats')
      .set('Authorization', `Bearer ${token}`);

    if (statsResponse.status !== 200) {
      throw new Error(`Get inventory stats failed: ${statsResponse.body.error}`);
    }

    console.log('   ✅ Get inventory stats successful');
    console.log(`   📊 Stats: ${JSON.stringify(statsResponse.body.data, null, 2)}`);

    // 更新库存项目
    const updateData = {
      quantity: 3,
      notes: '已消费2个'
    };

    const updateResponse = await request(app)
      .put(`/api/inventory/${inventoryItemId}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);

    if (updateResponse.status !== 200) {
      throw new Error(`Update inventory failed: ${updateResponse.body.error}`);
    }

    console.log('   ✅ Inventory item updated successfully');

    // 消费库存项目
    const consumeResponse = await request(app)
      .post(`/api/inventory/${inventoryItemId}/consume`)
      .set('Authorization', `Bearer ${token}`)
      .send({ quantity: 1 });

    if (consumeResponse.status !== 200) {
      throw new Error(`Consume inventory failed: ${consumeResponse.body.error}`);
    }

    console.log('   ✅ Inventory item consumed successfully');

    // 获取即将过期的食材
    const expiringResponse = await request(app)
      .get('/api/inventory/expiring?days=30')
      .set('Authorization', `Bearer ${token}`);

    if (expiringResponse.status !== 200) {
      throw new Error(`Get expiring items failed: ${expiringResponse.body.error}`);
    }

    console.log('   ✅ Get expiring items successful');
    console.log(`   ⏰ Expiring items count: ${expiringResponse.body.count}`);

    // 搜索库存
    const searchResponse = await request(app)
      .get('/api/inventory/search?q=测试')
      .set('Authorization', `Bearer ${token}`);

    if (searchResponse.status !== 200) {
      throw new Error(`Search inventory failed: ${searchResponse.body.error}`);
    }

    console.log('   ✅ Search inventory successful');
    console.log(`   🔍 Search results count: ${searchResponse.body.count}`);

    console.log('✅ Inventory API tests passed\n');

    // 测试批量操作
    console.log('📋 Test 3: Batch Operations...');

    // 批量添加库存
    const batchData = {
      items: [
        {
          food_item_id: foodItemId,
          quantity: 2,
          unit: '个',
          expiry_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          location: '冷藏室',
          notes: '批量测试1'
        },
        {
          food_item_id: foodItemId,
          quantity: 3,
          unit: '个',
          expiry_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          location: '冷藏室',
          notes: '批量测试2'
        }
      ]
    };

    const batchAddResponse = await request(app)
      .post('/api/inventory/batch')
      .set('Authorization', `Bearer ${token}`)
      .send(batchData);

    if (batchAddResponse.status !== 201) {
      throw new Error(`Batch add failed: ${batchAddResponse.body.error}`);
    }

    console.log('   ✅ Batch add successful');
    console.log(`   📦 Added: ${batchAddResponse.body.data.successCount} items`);

    console.log('✅ Batch operations tests passed\n');

    // 清理测试数据
    console.log('🧹 Cleaning up test data...');
    
    const finalInventoryResponse = await request(app)
      .get('/api/inventory')
      .set('Authorization', `Bearer ${token}`);

    if (finalInventoryResponse.status === 200) {
      const items = finalInventoryResponse.body.data;
      for (const item of items) {
        await request(app)
          .delete(`/api/inventory/${item.id}`)
          .set('Authorization', `Bearer ${token}`);
      }
      console.log(`   ✅ Cleaned up ${items.length} inventory items`);
    }

    console.log('✅ Test data cleaned up\n');

    console.log('🎉 All Inventory API tests passed!');
    console.log('📊 Test Summary:');
    console.log('   - ✅ User Authentication');
    console.log('   - ✅ Inventory CRUD Operations');
    console.log('   - ✅ Inventory Statistics');
    console.log('   - ✅ Expiring Items Detection');
    console.log('   - ✅ Inventory Search');
    console.log('   - ✅ Batch Operations');
    console.log('   - ✅ Data Cleanup');

  } catch (error) {
    console.error('❌ Inventory API test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.body);
    }
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testInventoryAPI().then(() => {
    console.log('\n✅ Test completed successfully');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = testInventoryAPI;