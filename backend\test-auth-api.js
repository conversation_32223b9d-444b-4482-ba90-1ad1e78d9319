const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/auth';

async function testAuthAPI() {
  console.log('🧪 Testing Authentication API...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);

    // Test 2: User registration
    console.log('\n2. Testing user registration...');
    const testUser = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'TestPassword123!'
    };

    const registerResponse = await axios.post(`${BASE_URL}/register`, testUser);
    console.log('✅ Registration successful:', {
      user: registerResponse.data.data.user,
      hasToken: !!registerResponse.data.data.token
    });

    const token = registerResponse.data.data.token;
    const userId = registerResponse.data.data.user.id;

    // Test 3: User login
    console.log('\n3. Testing user login...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful:', {
      user: loginResponse.data.data.user,
      hasToken: !!loginResponse.data.data.token
    });

    // Test 4: Get current user info
    console.log('\n4. Testing get current user info...');
    const meResponse = await axios.get(`${BASE_URL}/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ Get user info successful:', meResponse.data.data);

    // Test 5: Token refresh
    console.log('\n5. Testing token refresh...');
    const refreshResponse = await axios.post(`${BASE_URL}/refresh`, {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ Token refresh successful:', {
      user: refreshResponse.data.data.user,
      hasNewToken: !!refreshResponse.data.data.token,
      tokenChanged: refreshResponse.data.data.token !== token
    });

    // Test 6: Logout
    console.log('\n6. Testing logout...');
    const logoutResponse = await axios.post(`${BASE_URL}/logout`);
    console.log('✅ Logout successful:', logoutResponse.data);

    // Test 7: Error cases
    console.log('\n7. Testing error cases...');
    
    // Test invalid login
    try {
      await axios.post(`${BASE_URL}/login`, {
        email: testUser.email,
        password: 'WrongPassword123!'
      });
    } catch (error) {
      console.log('✅ Invalid login properly rejected:', error.response.data.message);
    }

    // Test duplicate registration
    try {
      await axios.post(`${BASE_URL}/register`, testUser);
    } catch (error) {
      console.log('✅ Duplicate registration properly rejected:', error.response.data.message);
    }

    // Test invalid token
    try {
      await axios.get(`${BASE_URL}/me`, {
        headers: {
          Authorization: 'Bearer invalid-token'
        }
      });
    } catch (error) {
      console.log('✅ Invalid token properly rejected:', error.response.data.message);
    }

    console.log('\n🎉 All authentication API tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run tests
testAuthAPI();