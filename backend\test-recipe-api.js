/**
 * 菜谱API测试脚本
 * 测试菜谱管理相关的所有API接口
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_URL = `${BASE_URL}/api`;

// 测试配置
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 测试用户数据
const testUser = {
  username: 'recipe_tester',
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let authToken = '';

/**
 * 测试工具函数
 */
function logTest(testName) {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log('=' .repeat(50));
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message, error) {
  console.log(`❌ ${message}`);
  if (error.response) {
    console.log(`   Status: ${error.response.status}`);
    console.log(`   Message: ${error.response.data?.message || error.response.statusText}`);
  } else {
    console.log(`   Error: ${error.message}`);
  }
}

/**
 * 用户认证测试
 */
async function testAuthentication() {
  logTest('User Authentication');
  
  try {
    // 注册用户
    try {
      await axios.post(`${API_URL}/auth/register`, testUser, testConfig);
      logSuccess('User registered successfully');
    } catch (error) {
      if (error.response?.status === 400 && error.response.data?.message?.includes('already exists')) {
        logSuccess('User already exists, continuing...');
      } else {
        throw error;
      }
    }
    
    // 登录用户
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    }, testConfig);
    
    authToken = loginResponse.data.data.token;
    logSuccess('User logged in successfully');
    
    return true;
  } catch (error) {
    logError('Authentication failed', error);
    return false;
  }
}

/**
 * 测试菜谱数据初始化
 */
async function testRecipeInitialization() {
  logTest('Recipe Data Initialization');
  
  try {
    const response = await axios.post(`${API_URL}/recipes/initialize`, {}, testConfig);
    
    if (response.data.success) {
      logSuccess(`Recipe initialization completed`);
      console.log(`   Success count: ${response.data.data.success_count}`);
      console.log(`   Error count: ${response.data.data.error_count}`);
      return true;
    } else {
      logError('Recipe initialization failed', { message: response.data.message });
      return false;
    }
  } catch (error) {
    logError('Recipe initialization failed', error);
    return false;
  }
}

/**
 * 测试获取所有菜谱
 */
async function testGetAllRecipes() {
  logTest('Get All Recipes');
  
  try {
    const response = await axios.get(`${API_URL}/recipes?limit=10`, testConfig);
    
    if (response.data.success && response.data.data.recipes) {
      logSuccess(`Retrieved ${response.data.data.recipes.length} recipes`);
      
      // 显示前3个菜谱的基本信息
      response.data.data.recipes.slice(0, 3).forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name} (${recipe.difficulty}, ${recipe.cooking_time}min)`);
      });
      
      return response.data.data.recipes;
    } else {
      logError('Failed to get recipes', { message: 'Invalid response format' });
      return [];
    }
  } catch (error) {
    logError('Failed to get recipes', error);
    return [];
  }
}

/**
 * 测试根据ID获取菜谱详情
 */
async function testGetRecipeById(recipes) {
  logTest('Get Recipe by ID');
  
  if (recipes.length === 0) {
    logError('No recipes available for testing', { message: 'Empty recipe list' });
    return;
  }
  
  try {
    const testRecipe = recipes[0];
    const response = await axios.get(`${API_URL}/recipes/${testRecipe.id}`, testConfig);
    
    if (response.data.success && response.data.data) {
      logSuccess(`Retrieved recipe details for: ${response.data.data.name}`);
      console.log(`   Ingredients: ${response.data.data.ingredients?.length || 0}`);
      console.log(`   Instructions: ${response.data.data.instructions?.length || 0} steps`);
      return response.data.data;
    } else {
      logError('Failed to get recipe details', { message: 'Invalid response format' });
    }
  } catch (error) {
    logError('Failed to get recipe details', error);
  }
}

/**
 * 测试搜索菜谱
 */
async function testSearchRecipes() {
  logTest('Search Recipes');
  
  try {
    const searchKeyword = '鸡蛋';
    const response = await axios.get(`${API_URL}/recipes/search/${searchKeyword}?limit=5`, testConfig);
    
    if (response.data.success && response.data.data) {
      logSuccess(`Found ${response.data.data.length} recipes for keyword: ${searchKeyword}`);
      
      response.data.data.forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name}`);
      });
      
      return response.data.data;
    } else {
      logError('Search failed', { message: 'Invalid response format' });
      return [];
    }
  } catch (error) {
    logError('Search failed', error);
    return [];
  }
}

/**
 * 测试获取热门菜谱
 */
async function testGetPopularRecipes() {
  logTest('Get Popular Recipes');
  
  try {
    const response = await axios.get(`${API_URL}/recipes/popular?limit=5`, testConfig);
    
    if (response.data.success && response.data.data) {
      logSuccess(`Retrieved ${response.data.data.length} popular recipes`);
      
      response.data.data.forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name} (Rating: ${recipe.rating || 'N/A'})`);
      });
      
      return response.data.data;
    } else {
      logError('Failed to get popular recipes', { message: 'Invalid response format' });
      return [];
    }
  } catch (error) {
    logError('Failed to get popular recipes', error);
    return [];
  }
}

/**
 * 测试获取菜谱分类统计
 */
async function testGetRecipeCategories() {
  logTest('Get Recipe Categories');
  
  try {
    const response = await axios.get(`${API_URL}/recipes/categories`, testConfig);
    
    if (response.data.success && response.data.data) {
      logSuccess('Retrieved recipe categories');
      
      console.log(`   Tags: ${response.data.data.tags?.length || 0}`);
      console.log(`   Difficulties: ${Object.keys(response.data.data.difficulties || {}).length}`);
      
      // 显示前5个热门标签
      if (response.data.data.tags && response.data.data.tags.length > 0) {
        console.log('   Top tags:');
        response.data.data.tags.slice(0, 5).forEach((tag, index) => {
          console.log(`     ${index + 1}. ${tag.tag} (${tag.count})`);
        });
      }
      
      return response.data.data;
    } else {
      logError('Failed to get categories', { message: 'Invalid response format' });
    }
  } catch (error) {
    logError('Failed to get categories', error);
  }
}

/**
 * 测试收藏功能（需要认证）
 */
async function testFavoriteFeatures(recipes) {
  logTest('Recipe Favorite Features');
  
  if (!authToken) {
    logError('Authentication required for favorite features', { message: 'No auth token' });
    return;
  }
  
  if (recipes.length === 0) {
    logError('No recipes available for testing favorites', { message: 'Empty recipe list' });
    return;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const testRecipe = recipes[0];
    
    // 添加收藏
    const favoriteResponse = await axios.post(
      `${API_URL}/recipes/${testRecipe.id}/favorite`, 
      {}, 
      authConfig
    );
    
    if (favoriteResponse.data.success) {
      logSuccess(`${favoriteResponse.data.data.action} favorite for recipe: ${testRecipe.name}`);
    }
    
    // 获取收藏列表
    const favoritesResponse = await axios.get(`${API_URL}/recipes/favorites`, authConfig);
    
    if (favoritesResponse.data.success) {
      logSuccess(`Retrieved ${favoritesResponse.data.data.length} favorite recipes`);
    }
    
  } catch (error) {
    logError('Favorite features test failed', error);
  }
}

/**
 * 测试智能推荐功能（需要认证）
 */
async function testSmartRecommendations() {
  logTest('Smart Recipe Recommendations');
  
  if (!authToken) {
    logError('Authentication required for smart recommendations', { message: 'No auth token' });
    return;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    const response = await axios.get(`${API_URL}/recipes/recommend/smart?limit=5`, authConfig);
    
    if (response.data.success && response.data.data) {
      logSuccess(`Retrieved ${response.data.data.length} smart recommendations`);
      
      response.data.data.forEach((recipe, index) => {
        console.log(`   ${index + 1}. ${recipe.name} (${recipe.recommendation_reason}, ${recipe.priority})`);
      });
      
      return response.data.data;
    } else {
      logError('Smart recommendations failed', { message: 'Invalid response format' });
    }
  } catch (error) {
    logError('Smart recommendations failed', error);
  }
}

/**
 * 测试基于食材的菜谱推荐（需要认证）
 */
async function testIngredientBasedRecommendations() {
  logTest('Ingredient-based Recipe Recommendations');
  
  if (!authToken) {
    logError('Authentication required for ingredient recommendations', { message: 'No auth token' });
    return;
  }
  
  const authConfig = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  try {
    // 模拟用户有的食材
    const mockIngredients = [
      { food_item_id: 'mock-tomato-id', quantity: 300 },
      { food_item_id: 'mock-egg-id', quantity: 5 }
    ];
    
    const response = await axios.post(
      `${API_URL}/recipes/recommend/by-ingredients`,
      {
        ingredients: mockIngredients,
        minMatchRatio: 0.3,
        limit: 5
      },
      authConfig
    );
    
    if (response.data.success && response.data.data) {
      logSuccess(`Retrieved ${response.data.data.length} ingredient-based recommendations`);
      
      response.data.data.forEach((recipe, index) => {
        const matchRatio = recipe.match_stats?.match_ratio || 0;
        console.log(`   ${index + 1}. ${recipe.name} (Match: ${(matchRatio * 100).toFixed(1)}%)`);
      });
      
      return response.data.data;
    } else {
      logError('Ingredient recommendations failed', { message: 'Invalid response format' });
    }
  } catch (error) {
    logError('Ingredient recommendations failed', error);
  }
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 Starting Recipe API Tests');
  console.log('=' .repeat(60));
  
  try {
    // 1. 认证测试
    const authSuccess = await testAuthentication();
    
    // 2. 初始化菜谱数据
    await testRecipeInitialization();
    
    // 3. 基础菜谱API测试
    const recipes = await testGetAllRecipes();
    await testGetRecipeById(recipes);
    await testSearchRecipes();
    await testGetPopularRecipes();
    await testGetRecipeCategories();
    
    // 4. 需要认证的功能测试
    if (authSuccess) {
      await testFavoriteFeatures(recipes);
      await testSmartRecommendations();
      await testIngredientBasedRecommendations();
    }
    
    console.log('\n🎉 All Recipe API tests completed!');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// 检查服务器是否运行
async function checkServerHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    if (response.data.status === 'OK') {
      console.log('✅ Server is running and healthy');
      return true;
    }
  } catch (error) {
    console.error('❌ Server health check failed. Please ensure the server is running on port 3001');
    console.error('   Run: npm run dev (in backend directory)');
    return false;
  }
}

// 启动测试
async function main() {
  console.log('🔍 Checking server health...');
  
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    process.exit(1);
  }
  
  await runAllTests();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  checkServerHealth
};