const { User, FoodItem, Inventory, Recipe, Family } = require('./src/models');

async function testModels() {
  try {
    console.log('🧪 Testing database operation layer (CRUD operations)...\n');
    
    // Test 1: User Model
    console.log('📋 Test 1: User Model CRUD Operations...');
    const user = new User();
    
    // Create user
    const newUser = await user.create({
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'TestPassword123!'
    });
    console.log('   ✅ User created:', newUser.username);
    
    // Find user by email
    const foundUser = await user.findByEmail(newUser.email);
    console.log('   ✅ User found by email:', foundUser.username);

    // Verify password
    const verifiedUser = await user.verifyPassword(newUser.email, 'TestPassword123!');
    console.log('   ✅ Password verified for:', verifiedUser.username);
    
    // Update user
    const updatedUser = await user.update(newUser.id, { username: `updateduser_${Date.now()}` });
    console.log('   ✅ User updated:', updatedUser.username);
    
    // Get inventory stats
    const stats = await user.getInventoryStats(newUser.id);
    console.log('   ✅ User inventory stats:', stats);
    
    console.log('✅ User Model tests passed\n');
    
    // Test 2: FoodItem Model
    console.log('📋 Test 2: FoodItem Model CRUD Operations...');
    const foodItem = new FoodItem();
    
    // Create food item
    const newFoodItem = await foodItem.create({
      name: '测试食材',
      category: '测试分类',
      default_expiry_days: 7,
      storage_method: '冷藏',
      nutrition_per_100g: { calories: 100, protein: 5 }
    });
    console.log('   ✅ Food item created:', newFoodItem.name);
    
    // Search food items
    const searchResults = await foodItem.search('测试');
    console.log('   ✅ Search results:', searchResults.length, 'items found');
    
    // Get categories
    const categories = await foodItem.getCategories();
    console.log('   ✅ Categories found:', categories.length);
    
    // Get nutrition info
    const nutrition = await foodItem.getNutritionInfo(newFoodItem.id);
    console.log('   ✅ Nutrition info:', nutrition);
    
    console.log('✅ FoodItem Model tests passed\n');
    
    // Test 3: Inventory Model
    console.log('📋 Test 3: Inventory Model CRUD Operations...');
    const inventory = new Inventory();
    
    // Add inventory item
    const inventoryItem = await inventory.addItem({
      user_id: newUser.id,
      food_item_id: newFoodItem.id,
      quantity: 500,
      unit: 'g',
      expiry_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    });
    console.log('   ✅ Inventory item added:', inventoryItem.quantity, inventoryItem.unit);
    
    // Get user inventory
    const userInventory = await inventory.getUserInventory(newUser.id);
    console.log('   ✅ User inventory items:', userInventory.length);
    
    // Get expiring items
    const expiringItems = await inventory.getExpiringItems(newUser.id, 10);
    console.log('   ✅ Expiring items:', expiringItems.length);
    
    // Get inventory stats
    const inventoryStats = await inventory.getInventoryStats(newUser.id);
    console.log('   ✅ Inventory stats:', inventoryStats);
    
    // Consume item
    await inventory.consumeItem(inventoryItem.id, 100);
    console.log('   ✅ Item consumed (100g)');
    
    console.log('✅ Inventory Model tests passed\n');
    
    // Test 4: Recipe Model
    console.log('📋 Test 4: Recipe Model CRUD Operations...');
    const recipe = new Recipe();
    
    // Create recipe
    const newRecipe = await recipe.create({
      name: '测试菜谱',
      description: '这是一个测试菜谱',
      cooking_time: 30,
      difficulty: 'easy',
      servings: 2,
      instructions: ['步骤1', '步骤2', '步骤3'],
      tags: ['测试', '简单'],
      nutrition_info: { calories: 200, protein: 10 }
    });
    console.log('   ✅ Recipe created:', newRecipe.name);
    
    // Create recipe with ingredients
    const recipeWithIngredients = await recipe.createWithIngredients({
      name: '测试菜谱2',
      description: '带食材的测试菜谱',
      cooking_time: 20,
      difficulty: 'medium',
      servings: 1
    }, [{
      food_item_id: newFoodItem.id,
      quantity: 200,
      unit: 'g',
      is_optional: 0
    }]);
    console.log('   ✅ Recipe with ingredients created:', recipeWithIngredients.name);
    
    // Search recipes
    const recipeSearchResults = await recipe.searchRecipes('测试');
    console.log('   ✅ Recipe search results:', recipeSearchResults.length);
    
    // Get recipes by ingredients
    const recommendedRecipes = await recipe.getRecipesByIngredients([{
      food_item_id: newFoodItem.id
    }], { userId: newUser.id });
    console.log('   ✅ Recommended recipes:', recommendedRecipes.length);
    
    // Toggle favorite
    const favoriteResult = await recipe.toggleFavorite(newUser.id, newRecipe.id);
    console.log('   ✅ Recipe favorited:', favoriteResult.favorited);
    
    console.log('✅ Recipe Model tests passed\n');
    
    // Test 5: Family Model
    console.log('📋 Test 5: Family Model CRUD Operations...');
    const family = new Family();
    
    // Create family
    const newFamily = await family.create({
      name: '测试家庭',
      admin_id: newUser.id
    });
    console.log('   ✅ Family created:', newFamily.name);
    
    // Create another user for family testing
    const secondUser = await user.create({
      username: 'familymember',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    // Add member
    await family.addMember(newFamily.id, secondUser.id, 'member');
    console.log('   ✅ Family member added');
    
    // Get members
    const members = await family.getMembers(newFamily.id, { includeStats: true });
    console.log('   ✅ Family members:', members.length);
    
    // Get user families
    const userFamilies = await family.getUserFamilies(newUser.id, { includeStats: true });
    console.log('   ✅ User families:', userFamilies.length);
    
    // Check if user is admin
    const isAdmin = await family.isAdmin(newFamily.id, newUser.id);
    console.log('   ✅ User is admin:', isAdmin);
    
    console.log('✅ Family Model tests passed\n');
    
    // Test 6: Transaction Testing
    console.log('📋 Test 6: Transaction Operations...');
    
    // Test batch operations
    const batchFoodItems = await foodItem.createMany([
      { name: '批量食材1', category: '测试', default_expiry_days: 5 },
      { name: '批量食材2', category: '测试', default_expiry_days: 10 },
      { name: '批量食材3', category: '测试', default_expiry_days: 15 }
    ]);
    console.log('   ✅ Batch food items created:', batchFoodItems.changes);
    
    // Test complex query with joins
    const complexQuery = await inventory.query(`
      SELECT 
        i.id,
        i.quantity,
        f.name as food_name,
        u.username,
        julianday(i.expiry_date) - julianday('now') as days_to_expiry
      FROM inventory i
      JOIN food_items f ON i.food_item_id = f.id
      JOIN users u ON i.user_id = u.id
      WHERE i.status = 'active'
      ORDER BY days_to_expiry ASC
      LIMIT 5
    `);
    console.log('   ✅ Complex query results:', complexQuery.length);
    
    console.log('✅ Transaction tests passed\n');
    
    // Test 7: Validation Testing
    console.log('📋 Test 7: Data Validation...');
    
    try {
      await user.create({
        username: 'x', // Too short
        email: 'invalid-email',
        password: '123' // Too short
      });
      console.log('   ❌ Validation should have failed');
    } catch (error) {
      console.log('   ✅ User validation working:', error.message.substring(0, 50) + '...');
    }
    
    try {
      await foodItem.create({
        name: '', // Empty name
        category: 'test'
      });
      console.log('   ❌ Validation should have failed');
    } catch (error) {
      console.log('   ✅ FoodItem validation working:', error.message.substring(0, 50) + '...');
    }
    
    try {
      await inventory.addItem({
        user_id: newUser.id,
        food_item_id: newFoodItem.id,
        quantity: -10, // Negative quantity
        expiry_date: 'invalid-date'
      });
      console.log('   ❌ Validation should have failed');
    } catch (error) {
      console.log('   ✅ Inventory validation working:', error.message.substring(0, 50) + '...');
    }
    
    console.log('✅ Validation tests passed\n');
    
    // Cleanup test data
    console.log('🧹 Cleaning up test data...');
    await family.deleteFamily(newFamily.id, newUser.id);
    await user.delete(newUser.id);
    await user.delete(secondUser.id);
    await foodItem.deleteMany({ category: '测试' });
    await foodItem.deleteMany({ category: '测试分类' });
    await recipe.deleteMany({ name: '测试菜谱' });
    await recipe.deleteMany({ name: '测试菜谱2' });
    console.log('✅ Test data cleaned up\n');
    
    console.log('🎉 All database operation layer tests passed!');
    console.log('📊 Test Summary:');
    console.log('   - User Model: ✅ CRUD, Authentication, Stats');
    console.log('   - FoodItem Model: ✅ CRUD, Search, Categories, Nutrition');
    console.log('   - Inventory Model: ✅ CRUD, Expiry Management, Stats');
    console.log('   - Recipe Model: ✅ CRUD, Recommendations, Favorites');
    console.log('   - Family Model: ✅ CRUD, Member Management, Permissions');
    console.log('   - Transactions: ✅ Batch Operations, Complex Queries');
    console.log('   - Validation: ✅ Data Integrity, Error Handling');
    
  } catch (error) {
    console.error('❌ Model test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    const { DatabaseWrapper } = require('./src/database/connection');
    await DatabaseWrapper.close();
  }
}

testModels();